# DrawOut Agent 测试用例

## 基础流程测试用例

### 测试用例 1: 完整流程 - 咖啡制作主题

```
用户输入: "我想创建一个关于手冲咖啡技巧的白板视频"

预期阶段: Phase 1 - Requirements Gathering
预期响应: 询问目标受众、视频时长、行动召唤等关键信息

用户回答: "目标受众是咖啡爱好者和初学者，希望90秒左右，想让观众订阅我们的咖啡教学频道"

预期响应: 询问视觉配置选项（宽高比、语调、视觉风格等）

用户回答: "16:9的横屏格式，友好的语调，简约的素描风格就好"

预期响应: 创建project.md并开始制作故事板
```

### 测试用例 1.1: 需要研究的主题

```
用户输入: "我想创建一个关于最新的可持续能源技术突破的白板视频，要包含2025年在氢能存储方面的重大进展"

预期阶段: Phase 1
预期响应: 询问目标受众等信息，并可能需要了解氢能存储技术的最新突破和实际应用

用户回答: "面向投资者和能源行业专业人士，120秒，希望他们关注我们的能源技术报告"

预期响应: 使用googleSearch搜索氢能存储最新进展，然后询问视觉配置

用户回答: "16:9格式，专业语调，详细的技术图解风格，使用蓝绿色调"

预期响应: 基于搜索结果创建project.md，包含最新的氢能技术信息
```

### 测试用例 2: 具体需求回应

```
用户输入: "目标受众是咖啡爱好者，视频时长90秒，希望观众购买我们的咖啡豆"

预期阶段: Phase 1 → Phase 2
预期响应: 确认其他视觉配置选项，然后创建 project.md
```

### 测试用例 3: 确认视觉配置

```
用户输入: "使用你推荐的配置就好"

预期阶段: Phase 2 → Phase 3
预期响应: 创建 project.md 文件，然后自动进入故事板创建
```

## 中断处理测试用例

### 测试用例 4: 需求变更

```
用户输入: "等等，把时长改成120秒，并且添加一个案例研究部分"

预期阶段: 任意阶段
预期响应: 更新 project.md 和 storyboard.md，调整待办事项列表
```

### 测试用例 5: 故事板修改

```
用户输入: "第二个场景的旁白太复杂了，能简化一下吗？"

预期阶段: Phase 3 或之后
预期响应: 更新 storyboard.md 中的第二个场景，重新生成相关资产
```

### 测试用例 6: 视觉风格调整

```
用户输入: "我觉得颜色方案应该改成单色的，看起来更专业"

预期阶段: Phase 2 或之后
预期响应: 更新 project.md 中的颜色方案，影响后续图像生成
```

## 具体主题测试用例

### 测试用例 7: 商业主题

```
用户输入: "创建一个解释数字化转型的白板视频，面向企业高管，60秒，希望他们预约咨询"

预期阶段: Phase 1 → Phase 2
预期响应: 直接创建项目配置，因为需求已经完整
```

### 测试用例 7.1: 新兴技术主题

```
用户输入: "我想创建一个关于量子计算如何改变金融行业的白板视频，需要包含最新的实际应用案例"

预期阶段: Phase 1
预期响应: 询问目标受众、视频时长等信息，并可能需要了解量子计算在金融领域的最新进展

用户回答: "目标是金融科技公司的技术决策者，90秒，希望他们预约我们的量子计算咨询服务"

预期响应: 使用googleSearch查询"quantum computing finance industry 2024 real applications breakthrough"，询问视觉配置

用户回答: "使用9:16竖屏格式（适合社交媒体），专业但不失活力的语调，现代科技风格"

预期响应: 基于搜索到的量子计算金融应用案例创建project.md
```

### 测试用例 8: 教育主题

```
用户输入: "我需要一个关于做饭基础技巧的教育视频，给大学生看的"

预期阶段: Phase 1
预期响应: 询问剩余配置参数（时长、行动召唤等）

用户回答: "60秒就够了，希望他们下载我们的健康食谱APP"

预期响应: 询问视觉配置选项

用户回答: "1:1正方形格式（适合Instagram），轻松友好的语调，手绘涂鸦风格"

预期响应: 创建project.md并开始制作故事板
```

### 测试用例 8.1: 科学教育主题

```
用户输入: "制作一个解释CRISPR基因编辑技术最新突破的教育视频，要包含2024年的诺贝尔奖相关成果"

预期阶段: Phase 1
预期响应: 询问目标受众等信息，可能需要了解CRISPR技术的最新发展和诺贝尔奖相关信息

用户回答: "面向生物技术专业的研究生和研究人员，150秒，鼓励他们参加我们的基因编辑研讨会"

预期响应: 执行googleSearch搜索"CRISPR gene editing 2024 Nobel Prize breakthrough latest developments"，询问视觉配置

用户回答: "16:9格式，学术但易懂的语调，详细的科学插图风格，使用实验室常见的蓝白配色"

预期响应: 整合搜索到的CRISPR最新成果信息创建project.md
```

### 测试用例 9: 技术主题

```
用户输入: "制作一个关于摄影构图技巧的视频，面向摄影爱好者，希望他们关注我们的摄影账号"

预期阶段: Phase 1
预期响应: 询问视频时长和其他配置选项

用户回答: "90秒，展示三分法、引导线和框架构图等基本技巧"

预期响应: 询问视觉配置

用户回答: "16:9格式，启发性的语调，艺术素描风格，黑白单色配色方案"

预期响应: 创建project.md，开始制作关于摄影构图的故事板
```

## 边界情况测试用例

### 测试用例 10: 超长时长请求

```
用户输入: "我想要一个300秒的视频"

预期响应: 建议最大时长为180秒，询问是否调整
```

### 测试用例 11: 模糊主题

```
用户输入: "创建一个关于技术的视频"

预期响应: 要求用户明确具体的技术主题
```

### 测试用例 12: 复杂配置请求

```
用户输入: "我想要16:9比例，专业语调，详细视觉风格，粗线条，三色配色方案，网格纸背景"

预期响应: 记录所有配置，询问缺失的核心信息（主题、受众等）
```

## 工作流程中断测试用例

### 测试用例 13: 资产生成中断

```
用户输入: "暂停一下，我想先看看第一个场景的图片效果"

预期阶段: Phase 5 (Asset Generation)
预期响应: 暂停当前生成，显示已完成的第一个场景图片
```

### 测试用例 14: 故事板重新设计

```
用户输入: "重新设计故事板，我想要4个场景而不是3个"

预期阶段: Phase 3 或之后
预期响应: 创建新版本故事板，更新待办事项列表
```

### 测试用例 15: 音频重新生成

```
用户输入: "第三个场景的音频听起来不太对，能用不同的声音重新生成吗？"

预期阶段: Phase 5
预期响应: 使用不同voice_id重新生成第三个场景的音频
```

## 高级功能测试用例

### 测试用例 16: 多轮优化

```
用户输入序列:
1. "创建健身减脂入门视频"
2. "面向初学者，90秒"
3. "第一个场景太快了，能延长到40秒吗？"
4. "最后添加一个成功案例的例子"

预期响应: 逐步构建和优化项目配置
```

### 测试用例 17: 品牌化要求

```
用户输入: "视频需要体现我们公司的专业形象，使用蓝色和白色的配色方案"

预期响应: 将品牌要求整合到视觉配置中
```

### 测试用例 17.1: 行业趋势主题

```
用户输入: "创建一个关于2024年AI在医疗诊断领域最新突破的视频，特别是在罕见疾病诊断方面的应用"

预期响应: 询问目标受众和其他配置，可能需要搜索AI医疗诊断的最新进展和具体案例

用户回答: "目标是医疗机构的管理者和医生，100秒，希望他们了解并采用我们的AI诊断系统"

预期响应: 使用googleSearch查询"AI medical diagnosis rare diseases 2024 breakthrough applications healthcare"，询问视觉配置

用户回答: "16:9格式，专业可信的语调，清晰的医疗图解风格，使用医疗行业标准的蓝绿色系"

预期响应: 根据搜索到的AI医疗诊断案例创建project.md
```

### 测试用例 18: 特定受众细分

```
用户输入: "目标受众是25-35岁的创业者，他们对新技术感兴趣但时间有限"

预期响应: 根据受众特征调整内容策略和视觉风格
```

## 错误处理测试用例

### 测试用例 19: 工具失败恢复

```
场景: 图像生成工具返回错误
预期响应: 重试图像生成或建议替代方案，不标记任务为完成
```

### 测试用例 20: 文件操作失败

```
场景: VFS写入失败
预期响应: 报告错误，提供解决方案或替代方法
```

## 性能测试用例

### 测试用例 21: 并行任务管理

```
场景: 同时处理多个场景的资产生成
预期行为: 最多3个并行任务，正确的状态追踪
```

### 测试用例 22: 大型项目处理

```
用户输入: "创建一个包含5个场景的复杂视频"
预期响应: 有效管理更大的任务列表和依赖关系
```

## 特殊主题测试用例

### 测试用例 23: 当前事件主题

```
用户输入: "制作一个关于SpaceX最新火星任务进展的视频，包括他们刚刚宣布的新技术突破"

预期响应: 询问配置信息，并可能需要搜索SpaceX最新的火星任务信息和技术公告

用户回答: "面向航天爱好者和科技投资者，120秒，希望他们关注我们的航天科技分析平台"

预期响应: 执行googleSearch搜索"SpaceX Mars mission 2024 latest announcement technology breakthrough"，询问视觉配置

用户回答: "16:9格式，充满激情和前瞻性的语调，未来科技风格的插图，使用太空主题的深蓝和橙色"

预期响应: 基于最新的SpaceX火星任务信息创建project.md
```

### 测试用例 24: 市场数据主题

```
用户输入: "创建一个解释当前全球芯片短缺情况及其对汽车行业影响的视频，需要最新的市场数据"

预期响应: 询问目标受众等信息，可能需要搜索当前芯片市场状况和汽车行业受影响的具体数据

用户回答: "汽车行业的供应链经理和采购主管，90秒，推广我们的供应链风险管理解决方案"

预期响应: 使用googleSearch查询"global chip shortage 2024 automotive industry impact latest market data statistics"，询问视觉配置

用户回答: "16:9格式，严谨专业的语调，数据可视化风格，使用商务蓝色系"

预期响应: 整合市场数据创建project.md，包含具体的短缺数据和行业影响
```

### 测试用例 25: 政策法规主题

```
用户输入: "我需要一个关于欧盟最新AI监管法案对科技公司影响的解释视频"

预期响应: 询问视频配置，可能需要了解欧盟AI法案的最新条款和对企业的具体要求

用户回答: "科技公司的合规官和法务团队，180秒，希望他们预约我们的AI合规咨询服务"

预期响应: 使用googleSearch查询"EU AI Act 2024 latest regulations requirements tech companies compliance"，询问视觉配置

用户回答: "16:9格式，权威但易理解的语调，清晰的信息图表风格，使用欧盟蓝和白色的配色"

预期响应: 根据搜索到的EU AI Act最新要求创建project.md，包含关键合规要点
```
